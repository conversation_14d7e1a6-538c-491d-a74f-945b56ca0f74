#This file is for functions that will parse a single room type
from helpers.LLM_Handler import LLM_Handler, openAIModels, geminiModels
from helpers.Document_Processing import Doc_Processor


class room_type_parser:

    def __init__(self, doc, property_name, room_type, model="gpt-4o-mini",):
        """
        Initialize the document processor with the specified model.

        Args:
            model (str): The model to use for processing. Default is "gpt-4o-mini".
        """
        if not (model in openAIModels or model in geminiModels):
            raise Exception(f"Error - unknown model: {model}")
        self.llm_handler = LLM_Handler(model)
        self.document_processor = Doc_Processor()
        self.set_parameters(doc,property_name, room_type)
        
    def set_parameters(self, doc, property_name, room_type):
        """
        Set the document, property name, and room type for the parser.

        Args:
            doc (str): The document to check.
            property_name (str): The property name.
            room_type (str): The room type.
        """
        self.doc = doc
        self.property_name = property_name
        self.room_type = room_type

    def get_meal_type(self, types):
        """
        Get the meal type available in a specific room type of a property.

        Args:
            types (list): List of meal types.

        Returns:
            str: The meal type.
        """
        prompt = f"""In the following document which should contain accommodation information, what meal basis is available in the property "{self.property_name}" and the room type "{self.room_type}"?
        
        Remember!!:
        - A communal kitchen, shared kitchen, or kitchenette typically indicates a self-catering or self-service arrangement.

        Return **ONLY** one of the following meal basis, with no other text: {types}"""
        response = self.llm_handler.sendMessageToLLM(self.doc,prompt)
        return response.strip()

    #RATES:
    #period?
    def get_room_capacity(self, sto=-1, num_attempts=3):
        """
        Get the capacity details for a specific room type during a specific period.

        Args:
            period (tuple): The period name and date range.
            sto (int, optional): The STO value. Defaults to -1.
            num_attempts (int, optional): Number of attempts to validate. Defaults to 3.

        Returns:
            dict: The room capacity information.
        """
        rateterm = "published" if sto == 0 else "STO" if sto == 1 else "base"
        prompt = f"""In the following document which should contain accommodation information, what is the maximum capacity of a "{self.room_type}" at "{self.property_name}".
        Return a YAML response with the following data:
        BASE_CAPACITY: the base capacity of the room, for instance 2 for two people.
        TOTAL_CAPACITY: the total capacity of the room, for instance 6. This would include adults as well as children.
        MAX_ADULTS: The maximum number of adults that may stay at the accommodation. 
        MAX_CHILDREN: The maximum number of children that may stay at the accommodation.

        Remember to only return data relating to "{self.room_type}" at "{self.property_name}" and not any other potentially similar accommodations at different properties. If you cannot find figures related to this specific location, rather return zeros."""
        return self.llm_handler.sendMessageToLLM_Expecting_Yaml_Response(self.doc, prompt, num_attempts=num_attempts)
    
    def get_single_room_rate(self, period, sto=-1, num_attempts=3):
        """
        Get the rate for hiring a room for one adult during a specific period.

        Args:
            period (tuple): The period name and date range.
            sto (int, optional): The STO value. Defaults to -1.
            num_attempts (int, optional): Number of attempts to validate. Defaults to 3.

        Returns:
            dict: The room rate information.
        """
        rateterm = "published" if sto==0 else "STO" if sto==1 else "base"
        additional_context = "Note that if there is a distinction between a published/rack rate and another rate (STO/NETT rate), return the STO rate." if sto!=0 else ""
        prompt = f"""In the following document which should contain accommodation information, what is the {rateterm} rate for hiring a  "{self.room_type}" at "{self.property_name}" for ONE adult during the "{period[0]} ({period[1]} - {period[2]})" period?
        Note that the document will likely be using the South African Rand currency. 
        {additional_context}
        Return a YAML response with the following data:
        IS_AVAILABLE: TRUE or FALSE depending on whether this room exists and allows this number of people.
        BASE_RATE: the base rate for this room for this amount of people.
        BASE_CAPACITY: the base capacity of the room, for instance 2 for two people.
        TOTAL_CAPACITY: the total capacity of the room, for instance 6.
        ADDITIONAL_PEOPLE: the number of additional people above the base capacity. For instance, if the base capacity is 2, as we only need to lodge 1 adult, which is below base capacity, additional people is 0.
        ADDITIONAL_FEES: A list of any additional fees required, for instance to add an adult, if going over the base number of guests allowed in the unit. If there are no additional fees (which is most likely for only a single adult), leave an empty list.
        
        Remember to only return data relating to "{self.room_type}" at "{self.property_name}", and not any other potentially similar accommodations at different properties. If you cannot find figures related to this specific location, rather return zeros."""
        return self.llm_handler.sendMessageToLLM_Expecting_Yaml_Response(self.doc,prompt,num_attempts=num_attempts)
       
    def get_double_room_rate(self, period, sto=-1, num_attempts=3):
        """
        Get the rate for hiring a room for two adults during a specific period.

        Args:
            period (tuple): The period name and date range.
            sto (int, optional): The STO value. Defaults to -1.
            num_attempts (int, optional): Number of attempts to validate. Defaults to 3.

        Returns:
            dict: The room rate information.
        """
        rateterm = "published" if sto==0 else "STO" if sto==1 else "base"
        additional_context = "Note that if there is a distinction between a published/rack rate and another rate (STO/NETT rate), return the STO rate." if sto!=0 else ""
        prompt = f"""In the following document which should contain accommodation information, what is the {rateterm} rate for hiring a  "{self.room_type}" at "{self.property_name}" for TWO adults during the "{period[0]} ({period[1]} - {period[2]})" period?
        Note that the document will likely be using the South African Rand currency. Do not include any currency symbol in your answer.
        {additional_context}
        Return a YAML response with the following data:
        IS_AVAILABLE: TRUE or FALSE depending on whether this room exists and allows this number of people.
        BASE_RATE: the base rate for this room for this amount of people.
        ADDITIONAL_FEES: A list of any additional fees required, for instance to add an adult, if going over the base number of guests allowed in the unit. If there are no additional fees (which is likely for only two adults), leave an empty list.

        Remember to only return data relating to "{self.room_type}" at "{self.property_name}"during "{period[0]}", and not any other potentially similar accommodations at different properties. If you cannot find figures related to this specific location, rather return zeros.
        
        As an example, if the room has a base guest amount of two, with a base rate of R500, and a cost of R200 per additional guest, and has 1 double, 2 single, 2 bench beds, that room can have up to 6 people. 
        Because we are trying to host two people, and the base guest amount is two, no additional fees are needed. As such, the fields should be as follows:
        IS_AVAILABLE: TRUE
        BASE_RATE: 500
        BASE_CAPACITY: 2
        TOTAL_CAPACITY: 6
        ADDITIONAL_PEOPLE: 0
        ADDITIONAL_FEES: []
        """
        return self.llm_handler.sendMessageToLLM_Expecting_Yaml_Response(self.doc,prompt,num_attempts=num_attempts)

    def get_triple_room_rate(self, period, sto=-1, num_attempts=3):
        """
        Get the rate for hiring a room for three adults during a specific period.

        Args:
            period (tuple): The period name and date range.
            sto (int, optional): The STO value. Defaults to -1.
            num_attempts (int, optional): Number of attempts to validate. Defaults to 3.

        Returns:
            dict: The room rate information.
        """
        rateterm = "published" if sto==0 else "STO" if sto==1 else "base"
        additional_context = "Note that if there is a distinction between a published/rack rate and another rate (STO/NETT rate), return the STO rate." if sto!=0 else ""
        prompt = f"""In the following document which should contain accommodation information, what is the {rateterm} rate for hiring a  "{self.room_type}" at "{self.property_name}" for THREE adults during the "{period[0]} ({period[1]} - {period[2]})" period?
        Note that the document will likely be using the South African Rand currency. Do not include any currency symbol in your answer.
        {additional_context}
        Return a YAML response with the following data:
        IS_AVAILABLE: TRUE or FALSE depending on whether this room exists and allows this number of people.
        BASE_RATE: the base rate for this room for this amount of people.
        ADDITIONAL_FEES: A list of any additional fees required, for instance to add an adult, if going over the base number of guests allowed in the unit. If there are no additional fees (i.e. if the base guests is over two), leave an empty list.
        
        Remember to only return data relating to "{self.room_type}" at "{self.property_name}" during "{period[0]}", and not any other potentially similar accommodations at different properties. If you cannot find figures related to this specific location, rather return zeros.

        As an example, if the room has a base guest amount of two, with a base rate of R500, and a cost of R200 per additional guest, and has 1 double, 2 single, 2 bench beds, that room can have up to 6 people. 
        Because we are trying to host 3 people, the fields should be as follows:
        IS_AVAILABLE: TRUE
        BASE_RATE: 500
        BASE_CAPACITY: the base capacity of the room, for instance 2 for two people.
        TOTAL_CAPACITY: the total capacity of the room, for instance 6.
        ADDITIONAL_PEOPLE: the number of additional people above the base capacity. For instance, if the base capacity is 2, we need 1 additional person, as we want to host three adults.
        ADDITIONAL_FEES: [200]
        
        Meanwhile, if a room only has 1 double bed available, and no other beds: 
        IS_AVAILABLE: FALSE
        BASE_RATE: 0
        BASE_CAPACITY: 2
        TOTAL_CAPACITY: 2
        ADDITIONAL_PEOPLE: 1
        ADDITIONAL_FEES: []"""
        return self.llm_handler.sendMessageToLLM_Expecting_Yaml_Response(self.doc,prompt,num_attempts=num_attempts)
        
    def get_quad_room_rate(self, period, sto=-1, num_attempts=3):
        """
        Get the rate for hiring a room for four adults during a specific period.

        Args:
            period (tuple): The period name and date range.
            sto (int, optional): The STO value. Defaults to -1.
            num_attempts (int, optional): Number of attempts to validate. Defaults to 3.

        Returns:
            dict: The room rate information.
        """
        rateterm = "published" if sto==0 else "STO" if sto==1 else "base"
        additional_context = "Note that if there is a distinction between a published/rack rate and another rate (STO/NETT rate), return the STO rate." if sto!=0 else ""
        prompt = f"""In the following document which should contain accommodation information, what is the {rateterm} rate for hiring a  "{self.room_type}" at "{self.property_name}" for FOUR adults during the "{period[0]} ({period[1]} - {period[2]})" period?
        Note that the document will likely be using the South African Rand currency. Do not include any currency symbol in your answer.
        {additional_context}
        Return a YAML response with the following data:
        IS_AVAILABLE: TRUE or FALSE depending on whether this room exists and allows this number of people.
        BASE_RATE: the base rate for this room for this amount of people.
        BASE_CAPACITY: the base capacity of the room, for instance 2 for two people.
        TOTAL_CAPACITY: the total capacity of the room, for instance 6.
        ADDITIONAL_PEOPLE: the number of additional people above the base capacity. For instance, if the base capacity is 2, we need 2 additional people, as we want to host four adults.
        ADDITIONAL_FEES: A list of any additional fees required, for instance per adult added, if going over the base number of guests allowed in the unit. If there are no additional fees (ie the base rate is four or above), leave an empty list.
        
        Remember to only return data relating to "{self.room_type}" at "{self.property_name}"during "{period[0]}", and not any other potentially similar accommodations at different properties. If you cannot find figures related to this specific location, rather return zeros.

        As an example, if the room has a base guest amount of two, with a base rate of R500, and a cost of R200 per additional guest, and has 1 double, 2 single, 2 bench beds, that room can have up to 6 people. 
        Because we are trying to host 4 people, the fields should be as follows:
        IS_AVAILABLE: TRUE
        BASE_RATE: 500
        BASE_CAPACITY: 2
        TOTAL_CAPACITY: 6
        ADDITIONAL_PEOPLE: 2
        ADDITIONAL_FEES: [200, 200]
        
        Meanwhile, if a room only has 3 single beds available: 
        IS_AVAILABLE: FALSE
        BASE_RATE: 0
        BASE_CAPACITY: 3
        TOTAL_CAPACITY: 3
        ADDITIONAL_PEOPLE: 1
        ADDITIONAL_FEES: []"""
        return self.llm_handler.sendMessageToLLM_Expecting_Yaml_Response(self.doc,prompt,num_attempts=num_attempts)

    def get_child_rate(self, period, sto=-1, num_attempts=3):
        """
        Get the additional cost for bringing a child to a room during a specific period.

        Args:
            period (tuple): The period name and date range.
            sto (int, optional): The STO value. Defaults to -1.
            num_attempts (int, optional): Number of attempts to validate. Defaults to 3.

        Returns:
            dict: The additional cost information.
        """
        rateterm = "published " if sto==0 else "STO " if sto==1 else ""
        additional_context = "Note that if there is a distinction between a published/rack rate and another rate (STO/NETT rate), return the STO rate." if sto!=0 else ""
        prompt = f"""In the following document which should contain accommodation information, what is the {rateterm}additional cost for bringing a child to a "{self.room_type}" at "{self.property_name}" during the "{period[0]} ({period[1]} - {period[2]})" period?
        Note that the document will likely be using the South African Rand currency. Do not include any currency symbol in your answer.
        {additional_context}
        Return only the fee that must be paid for bringing a child. If none is specified, this may be the same as the fee for an additional adult. If no additional fees can be found at all for this room, return 0. If a child is explicitly not allowed, return -1.
        Remember to only return data relating to "{self.room_type}" at "{self.property_name}"during "{period[0]}", and not any other potentially similar accommodations at different properties. If you cannot find figures related to this specific location, rather return zero.
        Return a YAML response with the following data:
        CHILD_RATE: value
        """
        for _ in range(num_attempts):
            try:
                # Attempt to get a response from the LLM
                response = self.llm_handler.sendMessageToLLM_Expecting_Yaml_Response(self.doc,prompt,num_attempts=num_attempts)
                if response.get("CHILD_RATE"):
                    return response["CHILD_RATE"]
            except Exception as e:
                print(e)
        return -1

    def get_child_2_rate(self, period, sto=-1, num_attempts=3):
        """
        Get the additional cost for bringing a second child to a room during a specific period.

        Args:
            period (tuple): The period name and date range.
            sto (int, optional): The STO value. Defaults to -1.
            num_attempts (int, optional): Number of attempts to validate. Defaults to 3.

        Returns:
            dict: The additional cost information.
        """
        rateterm = "published " if sto==0 else "STO " if sto==1 else ""
        additional_context = "Note that if there is a distinction between a published/rack rate and another rate (STO/NETT rate), return the STO rate." if sto!=0 else ""
        prompt = f"""In the following document which should contain accommodation information, what is the {rateterm}additional cost for bringing a second child to a "{self.room_type}" at "{self.property_name}" during the "{period[0]} ({period[1]} - {period[2]})" period?
        Note that the document will likely be using the South African Rand currency. Do not include any currency symbol in your answer.
        {additional_context}
        Return only the fee that must be paid for bringing a child. If none is specified, this may be the same as the fee for an additional adult. If no additional fees can be found at all for this room, return 0. If a second child is explicitly not allowed, return -1.
        Remember to only return data relating to "{self.room_type}" at "{self.property_name}"during "{period[0]}", and not any other potentially similar accommodations at different properties. If you cannot find figures related to this specific location, rather return zero.
        Return a YAML response with the following data:
        CHILD_RATE: value
        """
        for _ in range(num_attempts):
            try:
                # Attempt to get a response from the LLM
                response = self.llm_handler.sendMessageToLLM_Expecting_Yaml_Response(self.doc,prompt,num_attempts=num_attempts)
                if response.get("CHILD_RATE"):
                    return response["CHILD_RATE"]
            except Exception as e:
                print(e)
        return -1

    def get_infant_rate(self, period, sto=-1, num_attempts=3):
        """
        Get the additional cost for bringing an infant to a room during a specific period.

        Args:
            period (tuple): The period name and date range.
            sto (int, optional): The STO value. Defaults to -1.
            num_attempts (int, optional): Number of attempts to validate. Defaults to 3.

        Returns:
            dict: The additional cost information.
        """
        rateterm = "published " if sto==0 else "STO " if sto==1 else ""
        additional_context = "Note that if there is a distinction between a published/rack rate and another rate (STO/NETT rate), return the STO rate." if sto!=0 else ""
        prompt = f"""In the following document which should contain accommodation information, what is the {rateterm}additional cost for bringing an infant to a "{self.room_type}" at "{self.property_name}" during the "{period[0]} ({period[1]} - {period[2]})" period?
        Note that the document will likely be using the South African Rand currency. Do not include any currency symbol in your answer.
        {additional_context}
        Return only the fee that must be paid for bringing an infant. If this specific fee is not discussed anywhere for this room, return 0. If infants are explicitly not allowed, return -1.
        Remember to only return data relating to "{self.room_type}" at "{self.property_name}"during "{period[0]}", and not any other potentially similar accommodations at different properties. If you cannot find figures related to this specific location, rather return zero.
        Return a YAML response with the following data:
        INFANT_RATE: value
        """
        for _ in range(num_attempts):
            try:
                # Attempt to get a response from the LLM
                response = self.llm_handler.sendMessageToLLM_Expecting_Yaml_Response(self.doc,prompt,num_attempts=num_attempts)
                if response.get("INFANT_RATE"):
                    return response["INFANT_RATE"]
            except Exception as e:
                print(e)
        return -1
    
    def min_night_stay(self, num_attempts=3, attempt_num=0):
        prompt = f"""You are a travel agent, helping me make sense of some hotel documentation.
        Based on the following document, is there a minimum nights stay at a '{self.room_type}' at '{self.property_name}'? If yes, return only the number - eg 2. If not, return only 0. If the rule is more complex - for instance, the minimum night stay varies depending on on some other factors - return -1."""
        response = self.llm_handler.sendMessageToLLM(self.doc,prompt)
        response = response.strip()
        try:
            response_int = int(response)
            return response_int
        except ValueError:
            attempt_num+=1
            if attempt_num > num_attempts:
                print(f"error - never returned a single int after {attempt_num} attempts", response)
                return 0
            return self.min_night_stay(num_attempts,attempt_num)
        
    def min_night_stay_complex(self):
        prompt = f"""You are a travel agent, helping me make sense of some hotel documentation.
        Based on the following document, what is the minimum nights stay at a '{self.room_type}' at '{self.property_name}'? If it is dependant on some other factors, describe the rule"""
        response = self.llm_handler.sendMessageToLLM(self.doc,prompt)
        return response.strip()

    def cancellation_policy(self, num_attempts=3):
        prompt = f"""In the following document which should contain accommodation information, what is the cancellation policy at a "{self.room_type}" at "{self.property_name}" period?
        Note that the document will likely be using the South African Rand currency. Do not include any currency symbol in your answer.
        If no relevant data is found for "{self.room_type}" at "{self.property_name}" just return 0s.
        Return a YAML response with the following data:
        POLICIES: A list of cancellation policies that each contain the following information:
            START_DAY: The start date of the policy's validity, in relation to the day of the booking. For example, if the policy is "15-10 days before the stay: Pay 25% cancellation fee" the start_day would be 15.
            CANCELLATION_FEE: The fee for cancelling the booking in this date range, as an integer representing the percentage. For example, if the policy is "15-10 days before the stay: Pay 25% cancellation fee" the CANCELLATION_FEE would be 25.
        
        Remember to only return data relating to "{self.room_type}" at "{self.property_name}" and not any other potentially similar accommodations at different properties. If you cannot find figures related to this specific location, rather return zeros.

        """
        return self.llm_handler.sendMessageToLLM_Expecting_Yaml_Response(self.doc,prompt,num_attempts=num_attempts)
    
    def get_stay_type(self, num_attempts=3):
        """
        Get any pay stays/ specials information.

        Returns:
        list: List of the pay stays/ specials information.
            
        """
        prompt = f"""In the following accomodation document, look for any mentions of discounts for staying a certain period, such as "pay stay" offers or stay x days and get a free night .

        Return data that applies to "{self.property_name}" directly, or if the document states the offer applies to "all lodges" or to a group that includes "{self.property_name}", include that as well (unless the other properties are explicitly included or this one is explicitly excluded).

        - **Do NOT** include any pricing, rates, or cost-related details such as STO rates, nightly rates.  
        - Repeated information—each offer and description should be returned **only once**, each in a new line without any special characters or listing formats.

        Return a YAML response with the following data:
        OFFERS: A list of pay stay type offerts that each contain the following information:
            PAY_STAY_DAYS: The number of days that the guest must stay to get the offer. For example, if the offer is "Stay 3, Pay 2" the PAY_STAY_DAYS would be 3. If no relevant data is found, return 0.
            PAY_STAY_FREE_NIGHTS: The amount of nights that are free, e.g with "Stay 3, Pay 2" that is 1 night. If no relevant data is found, return 0.

        If you cannot find figures related to this specific location, **JUST RETURN ZEROS**. """
        
        return self.llm_handler.sendMessageToLLM_Expecting_Yaml_Response(self.doc,prompt,num_attempts=num_attempts)